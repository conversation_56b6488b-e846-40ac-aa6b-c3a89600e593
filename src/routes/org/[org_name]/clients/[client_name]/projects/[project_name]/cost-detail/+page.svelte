<script lang="ts">
	import type { PageData } from './$types';
	import type { CostDetailBudgetItem, WorkPackageData, PurchaseOrderData } from './+page.server';
	import { createGenericBudgetHierarchy } from '$lib/budget_utils';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { formatCurrency } from '$lib/schemas/purchase_order';
	import type { HierarchyNode } from 'd3-hierarchy';
	import type { WbsItemWithBudgetData } from '$lib/budget_utils';

	const { data }: { data: PageData } = $props();
	const { costDetailData, allWbsItems } = data;

	// State for hiding zero values
	let hideZeros = $state(true);

	// Create the hierarchical structure using the budget utils
	const costHierarchy = $derived(() => {
		if (!allWbsItems || !costDetailData) {
			return null;
		}
		return createGenericBudgetHierarchy(allWbsItems, costDetailData);
	});

	// Type for hierarchy node with cost detail data
	type CostDetailHierarchyNode = HierarchyNode<WbsItemWithBudgetData<CostDetailBudgetItem>>;

	// Filter function for zero values
	const shouldShowItem = (node: CostDetailHierarchyNode): boolean => {
		if (!hideZeros) return true;

		const budgetData = node.data.budgetData;

		// Show if has budget amount
		if (budgetData?.budget_amount && budgetData.budget_amount > 0) return true;

		// Show if has work packages
		if (budgetData?.work_packages && budgetData.work_packages.length > 0) return true;

		// Show if has purchase orders
		if (budgetData?.purchase_orders && budgetData.purchase_orders.length > 0) return true;

		// Show if has children with values
		if (node.children && node.children.some((child) => shouldShowItem(child))) return true;

		return false;
	};

	// Type for display row
	type DisplayRow = {
		id: string;
		wbs_code: string;
		wbs_description: string;
		budget_amount: number | null;
		work_packages: WorkPackageData[];
		purchase_orders: PurchaseOrderData[];
		indentClass: string;
		level: number;
	};

	// Recursive function to render hierarchy nodes
	function renderHierarchyRows(node: CostDetailHierarchyNode, level: number = 0): DisplayRow[] {
		if (hideZeros && !shouldShowItem(node)) return [];

		const indentClass = level > 0 ? `pl-${Math.min(level * 4, 16)}` : '';
		const budgetData = node.data.budgetData;
		const workPackages = budgetData?.work_packages || [];
		const purchaseOrders = budgetData?.purchase_orders || [];

		const rows: DisplayRow[] = [];

		// Add the main row for this node
		rows.push({
			id: node.data.wbs_library_item_id,
			wbs_code: budgetData?.wbs_code || node.data.code,
			wbs_description: budgetData?.wbs_description || node.data.description || '—',
			budget_amount: budgetData?.budget_amount || null,
			work_packages: workPackages,
			purchase_orders: purchaseOrders,
			indentClass,
			level,
		});

		// Add children rows
		if (node.children) {
			for (const child of node.children) {
				rows.push(...renderHierarchyRows(child, level + 1));
			}
		}

		return rows;
	}

	// Create display rows from hierarchy
	const displayRows = $derived.by((): DisplayRow[] => {
		if (!costHierarchy) return [];
		return renderHierarchyRows(costHierarchy as unknown as CostDetailHierarchyNode);
	});
</script>

<div class="container py-6">
	<div class="space-y-6">
		<div class="flex items-center justify-between">
			<div>
				<h1 class="text-3xl font-bold tracking-tight">Cost Detail</h1>
				<p class="text-muted-foreground">
					Hierarchical view of WBS codes, budget amounts, work packages, and purchase orders
				</p>
			</div>

			<div class="flex items-center space-x-2">
				<Checkbox id="hide-zeros" bind:checked={hideZeros} />
				<Label for="hide-zeros">Hide zero values</Label>
			</div>
		</div>

		{#if !costDetailData || costDetailData.length === 0}
			<div class="py-12 text-center">
				<p class="text-muted-foreground text-lg">No cost detail data found</p>
				<p class="text-muted-foreground mt-2 text-sm">
					Add budget items, work packages, or purchase orders to see cost details
				</p>
			</div>
		{:else if displayRows.length === 0}
			<div class="py-12 text-center">
				<p class="text-muted-foreground text-lg">No items to display</p>
				<p class="text-muted-foreground mt-2 text-sm">
					Try unchecking "Hide zero values" to see all items
				</p>
			</div>
		{:else}
			<div class="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>WBS Code</TableHead>
							<TableHead>Description</TableHead>
							<TableHead class="text-right">Budget Amount</TableHead>
							<TableHead>Work Packages</TableHead>
							<TableHead class="text-right">Purchase Orders</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{#each displayRows as row (row.id)}
							<TableRow>
								<TableCell class="font-medium">
									<div class={row.indentClass}>
										{row.wbs_code}
									</div>
								</TableCell>
								<TableCell class="text-muted-foreground">
									<div class={row.indentClass}>
										{row.wbs_description}
									</div>
								</TableCell>
								<TableCell class="text-right">
									{row.budget_amount ? formatCurrency(row.budget_amount) : '—'}
								</TableCell>
								<TableCell class="text-muted-foreground">
									{row.work_packages.length > 0
										? row.work_packages.map((wp) => wp.name).join(', ')
										: '—'}
								</TableCell>
								<TableCell class="text-muted-foreground text-right">
									{row.purchase_orders.length > 0
										? row.purchase_orders
												.map(
													(po) => `${po.po_number}${po.vendor_name ? ` (${po.vendor_name})` : ''}`,
												)
												.join(', ')
										: '—'}
								</TableCell>
							</TableRow>
						{/each}
					</TableBody>
				</Table>
			</div>
		{/if}
	</div>
</div>
