<script lang="ts">
	import type { PageData } from './$types';
	import type { CostDetailItem } from './+page.server';
	// import { createGenericBudgetHierarchy } from '$lib/budget_utils';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { formatCurrency } from '$lib/schemas/purchase_order';

	const { data }: { data: PageData } = $props();
	const { project, costDetailData } = data; // allWbsItems for future hierarchy

	// State for hiding zero values
	let hideZeros = $state(true);

	// TODO: Create the hierarchical structure using the budget utils
	// const costHierarchy = $derived(() => {
	// 	if (!allWbsItems || !costDetailData) {
	// 		return null;
	// 	}
	// 	return createGenericBudgetHierarchy(allWbsItems, costDetailData);
	// });

	// Filter function for zero values
	const shouldShowItem = (item: any): boolean => {
		if (!hideZeros) return true;

		// Show if has budget amount
		if (item.data?.budget_amount && item.data.budget_amount > 0) return true;

		// Show if has work packages
		if (item.data?.work_packages && item.data.work_packages.length > 0) return true;

		// Show if has purchase orders
		if (item.data?.purchase_orders && item.data.purchase_orders.length > 0) return true;

		// Show if has children with values
		if (item.children && item.children.some((child: any) => shouldShowItem(child))) return true;

		return false;
	};

	// Type for display row
	type DisplayRow = {
		id: string;
		wbs_code: string;
		wbs_description: string;
		budget_amount: number | null;
		work_packages: any[];
		purchase_orders: any[];
		indentClass: string;
		level: number;
	};

	// Recursive function to render hierarchy nodes
	function renderHierarchyRows(node: any, level: number = 0): DisplayRow[] {
		if (hideZeros && !shouldShowItem(node)) return [];

		const indentClass = level > 0 ? `pl-${Math.min(level * 4, 16)}` : '';
		const hasData = node.data;
		const workPackages = hasData?.work_packages || [];
		const purchaseOrders = hasData?.purchase_orders || [];

		const rows: DisplayRow[] = [];

		// Add the main row for this node
		rows.push({
			id: node.id,
			wbs_code: node.data?.wbs_code || node.id,
			wbs_description: node.data?.wbs_description || '—',
			budget_amount: hasData?.budget_amount,
			work_packages: workPackages,
			purchase_orders: purchaseOrders,
			indentClass,
			level,
		});

		// Add children rows
		if (node.children) {
			for (const child of node.children) {
				rows.push(...renderHierarchyRows(child, level + 1));
			}
		}

		return rows;
	}

	// TODO: Implement hierarchy display later
	// let displayRows = $derived(() => {
	// 	if (!costHierarchy) return [];
	// 	return renderHierarchyRows(costHierarchy);
	// });
</script>

<div class="container py-6 ">
	<div class="space-y-6">
		<div class="flex items-center justify-between">
			<div>
				<h1 class="text-3xl font-bold tracking-tight">Cost Detail</h1>
				<p class="text-muted-foreground">
					Hierarchical view of WBS codes, budget amounts, work packages, and purchase orders
				</p>
			</div>

			<div class="flex items-center space-x-2">
				<Checkbox id="hide-zeros" bind:checked={hideZeros} />
				<Label for="hide-zeros">Hide zero values</Label>
			</div>
		</div>

		{#if !costDetailData || costDetailData.length === 0}
			<div class="py-12 text-center">
				<p class="text-muted-foreground text-lg">No cost detail data found</p>
				<p class="text-muted-foreground mt-2 text-sm">
					Add budget items, work packages, or purchase orders to see cost details
				</p>
			</div>
		{:else}
			<div class="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>WBS Code</TableHead>
							<TableHead>Description</TableHead>
							<TableHead class="text-right">Budget Amount</TableHead>
							<TableHead>Work Packages</TableHead>
							<TableHead>Purchase Orders</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{#each costDetailData as item (item.wbs_library_item_id)}
							{@const workPackages = Array.isArray(item.work_packages) ? item.work_packages : []}
							{@const purchaseOrders = Array.isArray(item.purchase_orders)
								? item.purchase_orders
								: []}
							{#if !hideZeros || shouldShowItem({ data: item })}
								<TableRow>
									<TableCell class="font-medium">
										{item.wbs_code}
									</TableCell>
									<TableCell class="text-muted-foreground">
										{item.wbs_description}
									</TableCell>
									<TableCell class="text-right">
										{item.budget_amount ? formatCurrency(item.budget_amount) : '—'}
									</TableCell>
									<TableCell class="text-muted-foreground">
										{workPackages.length > 0
											? workPackages.map((wp: any) => wp.name).join(', ')
											: '—'}
									</TableCell>
									<TableCell class="text-muted-foreground">
										{purchaseOrders.length > 0
											? purchaseOrders
													.map(
														(po: any) =>
															`${po.po_number}${po.vendor_name ? ` (${po.vendor_name})` : ''}`,
													)
													.join(', ')
											: '—'}
									</TableCell>
								</TableRow>
							{/if}
						{/each}
					</TableBody>
				</Table>
			</div>
		{/if}
	</div>
</div>
